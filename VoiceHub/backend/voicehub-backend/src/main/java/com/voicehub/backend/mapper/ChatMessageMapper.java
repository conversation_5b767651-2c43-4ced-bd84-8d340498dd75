package com.voicehub.backend.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.voicehub.backend.entity.ChatMessage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Delete;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 聊天消息Mapper接口
 * 继承MyBatis-Plus的BaseMapper，提供基础CRUD功能
 */
@Mapper
public interface ChatMessageMapper extends BaseMapper<ChatMessage> {

    /**
     * 根据对话ID查找消息，按创建时间升序
     * MyBatis-Plus会自动添加逻辑删除条件
     */
    @Select("SELECT * FROM chat_messages WHERE conversation_id = #{conversationId} ORDER BY created_at ASC")
    List<ChatMessage> findByConversationIdOrderByCreatedAtAsc(@Param("conversationId") Long conversationId);

    /**
     * 分页查找对话消息
     * MyBatis-Plus会自动添加逻辑删除条件
     */
    @Select("SELECT * FROM chat_messages WHERE conversation_id = #{conversationId} ORDER BY created_at ASC")
    IPage<ChatMessage> findByConversationIdOrderByCreatedAtAsc(@Param("conversationId") Long conversationId, Page<ChatMessage> page);

    /**
     * 根据对话ID和角色查找消息
     * MyBatis-Plus会自动添加逻辑删除条件
     */
    @Select("SELECT * FROM chat_messages WHERE conversation_id = #{conversationId} AND role = #{role} ORDER BY created_at ASC")
    List<ChatMessage> findByConversationIdAndRoleOrderByCreatedAtAsc(@Param("conversationId") Long conversationId, @Param("role") String role);

    /**
     * 根据对话ID和消息类型查找消息
     * MyBatis-Plus会自动添加逻辑删除条件
     */
    @Select("SELECT * FROM chat_messages WHERE conversation_id = #{conversationId} AND type = #{type} ORDER BY created_at ASC")
    List<ChatMessage> findByConversationIdAndTypeOrderByCreatedAtAsc(@Param("conversationId") Long conversationId, @Param("type") String type);

    /**
     * 查找对话中的语音消息
     * MyBatis-Plus会自动添加逻辑删除条件
     */
    @Select("SELECT * FROM chat_messages WHERE conversation_id = #{conversationId} AND is_voice_input = true ORDER BY created_at ASC")
    List<ChatMessage> findVoiceMessagesByConversationId(@Param("conversationId") Long conversationId);

    /**
     * 查找对话的最后一条消息
     * MyBatis-Plus会自动添加逻辑删除条件
     */
    @Select("SELECT * FROM chat_messages WHERE conversation_id = #{conversationId} ORDER BY created_at DESC LIMIT 1")
    ChatMessage findLastMessageByConversationId(@Param("conversationId") Long conversationId);

    /**
     * 查找对话的最后N条消息
     * MyBatis-Plus会自动添加逻辑删除条件
     */
    @Select("SELECT * FROM chat_messages WHERE conversation_id = #{conversationId} ORDER BY created_at DESC LIMIT #{limit}")
    List<ChatMessage> findLastNMessagesByConversationId(@Param("conversationId") Long conversationId, @Param("limit") Integer limit);

    /**
     * 搜索用户的消息内容
     */
    @Select("SELECT cm.* FROM chat_messages cm " +
            "JOIN conversations c ON cm.conversation_id = c.id " +
            "WHERE c.user_id = #{userId} AND cm.content ILIKE CONCAT('%', #{keyword}, '%') " +
            "ORDER BY cm.created_at DESC")
    List<ChatMessage> searchUserMessages(@Param("userId") Long userId, @Param("keyword") String keyword);

    /**
     * 查找用户今天的消息
     */
    @Select("SELECT cm.* FROM chat_messages cm " +
            "JOIN conversations c ON cm.conversation_id = c.id " +
            "WHERE c.user_id = #{userId} AND DATE(cm.created_at) = CURRENT_DATE " +
            "ORDER BY cm.created_at DESC")
    List<ChatMessage> findTodayMessagesByUserId(@Param("userId") Long userId);

    /**
     * 查找用户本周的消息
     */
    @Select("SELECT cm.* FROM chat_messages cm " +
            "JOIN conversations c ON cm.conversation_id = c.id " +
            "WHERE c.user_id = #{userId} AND cm.created_at >= #{weekStart} " +
            "ORDER BY cm.created_at DESC")
    List<ChatMessage> findThisWeekMessagesByUserId(@Param("userId") Long userId, @Param("weekStart") LocalDateTime weekStart);

    /**
     * 查找用户本月的消息
     */
    @Select("SELECT cm.* FROM chat_messages cm " +
            "JOIN conversations c ON cm.conversation_id = c.id " +
            "WHERE c.user_id = #{userId} AND cm.created_at >= #{monthStart} " +
            "ORDER BY cm.created_at DESC")
    List<ChatMessage> findThisMonthMessagesByUserId(@Param("userId") Long userId, @Param("monthStart") LocalDateTime monthStart);

    /**
     * 查找用户指定时间范围内的消息
     */
    @Select("SELECT cm.* FROM chat_messages cm " +
            "JOIN conversations c ON cm.conversation_id = c.id " +
            "WHERE c.user_id = #{userId} AND cm.created_at >= #{startDate} AND cm.created_at <= #{endDate} " +
            "ORDER BY cm.created_at DESC")
    List<ChatMessage> findMessagesByUserIdAndDateRange(@Param("userId") Long userId,
                                                       @Param("startDate") LocalDateTime startDate,
                                                       @Param("endDate") LocalDateTime endDate);

    /**
     * 统计对话消息总数
     */
    @Select("SELECT COUNT(*) FROM chat_messages WHERE conversation_id = #{conversationId}")
    long countByConversationId(@Param("conversationId") Long conversationId);

    /**
     * 统计用户消息总数
     */
    @Select("SELECT COUNT(cm.*) FROM chat_messages cm " +
            "JOIN conversations c ON cm.conversation_id = c.id " +
            "WHERE c.user_id = #{userId}")
    long countByUserId(@Param("userId") Long userId);

    /**
     * 统计用户今天的消息数
     */
    @Select("SELECT COUNT(cm.*) FROM chat_messages cm " +
            "JOIN conversations c ON cm.conversation_id = c.id " +
            "WHERE c.user_id = #{userId} AND DATE(cm.created_at) = CURRENT_DATE")
    long countTodayMessagesByUserId(@Param("userId") Long userId);

    /**
     * 统计用户本周的消息数
     */
    @Select("SELECT COUNT(cm.*) FROM chat_messages cm " +
            "JOIN conversations c ON cm.conversation_id = c.id " +
            "WHERE c.user_id = #{userId} AND cm.created_at >= #{weekStart}")
    long countThisWeekMessagesByUserId(@Param("userId") Long userId, @Param("weekStart") LocalDateTime weekStart);

    /**
     * 根据ID和对话ID查找消息（安全检查）
     */
    @Select("SELECT * FROM chat_messages WHERE id = #{id} AND conversation_id = #{conversationId}")
    ChatMessage findByIdAndConversationId(@Param("id") Long id, @Param("conversationId") Long conversationId);

    // ========== Service层需要的额外方法 ==========

    /**
     * 根据对话ID查询消息
     */
    @Select("SELECT * FROM chat_messages WHERE conversation_id = #{conversationId} ORDER BY created_at ASC")
    List<ChatMessage> findByConversationId(@Param("conversationId") Long conversationId);

    /**
     * 分页查询对话消息
     */
    @Select("SELECT * FROM chat_messages WHERE conversation_id = #{conversationId} ORDER BY created_at DESC")
    IPage<ChatMessage> findByConversationIdWithPage(@Param("conversationId") Long conversationId, Page<ChatMessage> page);

    /**
     * 删除对话的所有消息
     */
    @Delete("DELETE FROM chat_messages WHERE conversation_id = #{conversationId}")
    void deleteByConversationId(@Param("conversationId") Long conversationId);

    /**
     * 搜索用户消息
     */
    @Select("SELECT cm.* FROM chat_messages cm JOIN conversations c ON cm.conversation_id = c.id WHERE c.user_id = #{userId} AND cm.content LIKE CONCAT('%', #{keyword}, '%')")
    List<ChatMessage> searchByUserIdAndKeyword(@Param("userId") Long userId, @Param("keyword") String keyword);

    /**
     * 根据情感查询消息
     */
    @Select("SELECT cm.* FROM chat_messages cm JOIN conversations c ON cm.conversation_id = c.id WHERE c.user_id = #{userId} AND cm.emotion_detected = #{emotion}")
    List<ChatMessage> findByUserIdAndEmotion(@Param("userId") Long userId, @Param("emotion") String emotion);

    /**
     * 获取情感统计
     */
    @Select("SELECT emotion_detected as emotion, COUNT(*) as count FROM chat_messages WHERE conversation_id IN (SELECT id FROM conversations WHERE user_id = #{userId}) AND emotion_detected IS NOT NULL GROUP BY emotion_detected")
    List<Map<String, Object>> getEmotionStatsByUserId(@Param("userId") Long userId);
}
