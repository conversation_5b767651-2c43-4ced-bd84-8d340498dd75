package com.voicehub.backend.service;

import com.voicehub.backend.entity.Notification;
import com.voicehub.backend.mapper.NotificationMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 通知服务实现
 */
@Service
@Transactional
public class NotificationService extends BaseServiceImpl<NotificationMapper, Notification> {

    @Autowired
    private NotificationMapper notificationMapper;

    /**
     * 创建通知
     */
    public Notification createNotification(Long userId, String type, String title, String message) {
        return createNotification(userId, type, title, message, null, "NORMAL", null, null);
    }

    /**
     * 创建通知（完整参数）
     */
    public Notification createNotification(Long userId, String type, String title, String message, 
                                         Map<String, Object> data, String priority, 
                                         LocalDateTime scheduledFor, LocalDateTime expiresAt) {
        Notification notification = new Notification();
        notification.setUserId(userId);
        notification.setType(type);
        notification.setTitle(title);
        notification.setMessage(message);
        notification.setData(data);
        notification.setPriority(priority != null ? priority : "NORMAL");
        notification.setScheduledFor(scheduledFor);
        notification.setExpiresAt(expiresAt);
        notification.setIsRead(false);
        notification.setCreatedAt(LocalDateTime.now());
        notification.setUpdatedAt(LocalDateTime.now());
        
        notificationMapper.insert(notification);
        return notification;
    }

    /**
     * 根据用户ID获取通知列表
     */
    public List<Notification> getByUserId(Long userId) {
        return notificationMapper.findByUserId(userId);
    }

    /**
     * 根据用户ID获取未读通知
     */
    public List<Notification> getUnreadByUserId(Long userId) {
        return notificationMapper.findUnreadByUserId(userId);
    }

    /**
     * 根据用户ID获取已读通知
     */
    public List<Notification> getReadByUserId(Long userId) {
        return notificationMapper.findReadByUserId(userId);
    }

    /**
     * 根据用户ID和类型获取通知
     */
    public List<Notification> getByUserIdAndType(Long userId, String type) {
        return notificationMapper.findByUserIdAndType(userId, type);
    }

    /**
     * 根据用户ID和优先级获取通知
     */
    public List<Notification> getByUserIdAndPriority(Long userId, String priority) {
        return notificationMapper.findByUserIdAndPriority(userId, priority);
    }

    /**
     * 标记通知为已读
     */
    public boolean markAsRead(Long notificationId) {
        notificationMapper.markAsRead(notificationId);
        return true;
    }

    /**
     * 标记用户所有通知为已读
     */
    public boolean markAllAsRead(Long userId) {
        notificationMapper.markAllAsReadByUserId(userId);
        return true;
    }

    /**
     * 标记用户特定类型通知为已读
     */
    public boolean markAsReadByType(Long userId, String type) {
        notificationMapper.markAsReadByUserIdAndType(userId, type);
        return true;
    }

    /**
     * 获取用户未读通知数量
     */
    public long getUnreadCount(Long userId) {
        return notificationMapper.countUnreadByUserId(userId);
    }

    /**
     * 获取用户通知总数
     */
    public long getTotalCount(Long userId) {
        return notificationMapper.countByUserId(userId);
    }

    /**
     * 获取计划发送的通知
     */
    public List<Notification> getScheduledNotifications() {
        return notificationMapper.findScheduledNotifications(LocalDateTime.now());
    }

    /**
     * 获取已过期的通知
     */
    public List<Notification> getExpiredNotifications() {
        return notificationMapper.findExpiredNotifications(LocalDateTime.now());
    }

    /**
     * 删除过期的通知
     */
    public void deleteExpiredNotifications() {
        notificationMapper.deleteExpiredNotifications(LocalDateTime.now());
    }

    /**
     * 删除通知
     */
    public boolean deleteNotification(Long notificationId) {
        Notification notification = notificationMapper.selectById(notificationId);
        if (notification != null) {
            notificationMapper.deleteById(notificationId);
            return true;
        }
        return false;
    }

    /**
     * 批量删除用户的已读通知
     */
    public boolean deleteReadNotifications(Long userId) {
        List<Notification> readNotifications = notificationMapper.findReadByUserId(userId);
        for (Notification notification : readNotifications) {
            notificationMapper.deleteById(notification.getId());
        }
        return true;
    }

    /**
     * 创建系统通知
     */
    public Notification createSystemNotification(String title, String message) {
        return createSystemNotification(title, message, "NORMAL", null);
    }

    /**
     * 创建系统通知（带优先级和过期时间）
     */
    public Notification createSystemNotification(String title, String message, String priority, LocalDateTime expiresAt) {
        // 系统通知发送给所有用户，这里简化处理，实际应该有专门的系统通知机制
        return createNotification(null, "SYSTEM", title, message, null, priority, null, expiresAt);
    }

    /**
     * 创建提醒通知
     */
    public Notification createReminderNotification(Long userId, String title, String message, LocalDateTime scheduledFor) {
        return createNotification(userId, "REMINDER", title, message, null, "NORMAL", scheduledFor, null);
    }

    /**
     * 创建成就通知
     */
    public Notification createAchievementNotification(Long userId, String achievementName, String description) {
        String title = "恭喜获得新成就！";
        String message = String.format("您获得了成就：%s - %s", achievementName, description);
        return createNotification(userId, "ACHIEVEMENT", title, message, null, "HIGH", null, null);
    }

    /**
     * 创建情绪检查通知
     */
    public Notification createMoodCheckNotification(Long userId) {
        String title = "情绪记录提醒";
        String message = "今天过得怎么样？记录一下您的心情吧！";
        return createNotification(userId, "MOOD_CHECK", title, message, null, "LOW", null, null);
    }

    /**
     * 获取用户通知统计
     */
    public NotificationStats getUserNotificationStats(Long userId) {
        long totalNotifications = notificationMapper.countByUserId(userId);
        long unreadNotifications = notificationMapper.countUnreadByUserId(userId);
        
        NotificationStats stats = new NotificationStats();
        stats.setTotalNotifications(totalNotifications);
        stats.setUnreadNotifications(unreadNotifications);
        stats.setReadNotifications(totalNotifications - unreadNotifications);
        
        return stats;
    }

    // 内部类：通知统计
    public static class NotificationStats {
        private long totalNotifications;
        private long unreadNotifications;
        private long readNotifications;

        // Getters and Setters
        public long getTotalNotifications() { return totalNotifications; }
        public void setTotalNotifications(long totalNotifications) { this.totalNotifications = totalNotifications; }
        public long getUnreadNotifications() { return unreadNotifications; }
        public void setUnreadNotifications(long unreadNotifications) { this.unreadNotifications = unreadNotifications; }
        public long getReadNotifications() { return readNotifications; }
        public void setReadNotifications(long readNotifications) { this.readNotifications = readNotifications; }
    }
}
