package com.voicehub.backend.controller;

import com.voicehub.backend.entity.User;
import com.voicehub.backend.entity.UserProfile;
import com.voicehub.backend.service.UserProfileService;
import com.voicehub.backend.dto.ApiResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * 用户详细资料控制器
 */
@RestController
@RequestMapping("/api/user-profile")
@Tag(name = "用户资料", description = "用户详细资料管理API")
public class UserProfileController {

    @Autowired
    private UserProfileService userProfileService;

    /**
     * 获取当前用户资料
     */
    @GetMapping("/me")
    @Operation(summary = "获取当前用户资料", description = "获取当前登录用户的详细资料")
    public ResponseEntity<ApiResponse<UserProfile>> getCurrentUserProfile(
            @AuthenticationPrincipal User currentUser) {
        
        UserProfile profile = userProfileService.getByUserId(currentUser.getId());
        
        if (profile != null) {
            return ResponseEntity.ok(ApiResponse.success(profile));
        } else {
            return ResponseEntity.ok(ApiResponse.success(null, "用户资料不存在"));
        }
    }

    /**
     * 创建或更新用户资料
     */
    @PostMapping("/me")
    @Operation(summary = "创建或更新用户资料", description = "创建或更新当前用户的详细资料")
    public ResponseEntity<ApiResponse<UserProfile>> createOrUpdateProfile(
            @AuthenticationPrincipal User currentUser,
            @Valid @RequestBody UserProfile profileRequest) {
        
        try {
            UserProfile profile = userProfileService.createOrUpdateProfile(currentUser.getId(), profileRequest);
            return ResponseEntity.ok(ApiResponse.success(profile, "用户资料保存成功"));
        } catch (Exception e) {
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("保存用户资料失败: " + e.getMessage()));
        }
    }

    /**
     * 检查用户是否有资料
     */
    @GetMapping("/me/exists")
    @Operation(summary = "检查用户资料是否存在", description = "检查当前用户是否已创建详细资料")
    public ResponseEntity<ApiResponse<Boolean>> hasProfile(
            @AuthenticationPrincipal User currentUser) {
        
        boolean hasProfile = userProfileService.hasProfile(currentUser.getId());
        return ResponseEntity.ok(ApiResponse.success(hasProfile));
    }

    /**
     * 更新隐私设置
     */
    @PutMapping("/me/privacy")
    @Operation(summary = "更新隐私设置", description = "更新用户的隐私设置")
    public ResponseEntity<ApiResponse<String>> updatePrivacySettings(
            @AuthenticationPrincipal User currentUser,
            @RequestBody Map<String, Object> privacySettings) {
        
        boolean success = userProfileService.updatePrivacySettings(currentUser.getId(), privacySettings);
        
        if (success) {
            return ResponseEntity.ok(ApiResponse.success("隐私设置更新成功"));
        } else {
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("更新隐私设置失败"));
        }
    }

    /**
     * 添加兴趣
     */
    @PostMapping("/me/interests")
    @Operation(summary = "添加兴趣", description = "为当前用户添加新的兴趣")
    public ResponseEntity<ApiResponse<String>> addInterest(
            @AuthenticationPrincipal User currentUser,
            @RequestParam @Parameter(description = "兴趣名称") String interest) {
        
        boolean success = userProfileService.addInterest(currentUser.getId(), interest);
        
        if (success) {
            return ResponseEntity.ok(ApiResponse.success("兴趣添加成功"));
        } else {
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("添加兴趣失败，可能已存在或用户资料不存在"));
        }
    }

    /**
     * 移除兴趣
     */
    @DeleteMapping("/me/interests")
    @Operation(summary = "移除兴趣", description = "移除当前用户的指定兴趣")
    public ResponseEntity<ApiResponse<String>> removeInterest(
            @AuthenticationPrincipal User currentUser,
            @RequestParam @Parameter(description = "兴趣名称") String interest) {
        
        boolean success = userProfileService.removeInterest(currentUser.getId(), interest);
        
        if (success) {
            return ResponseEntity.ok(ApiResponse.success("兴趣移除成功"));
        } else {
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("移除兴趣失败，可能不存在或用户资料不存在"));
        }
    }

    /**
     * 添加目标
     */
    @PostMapping("/me/goals")
    @Operation(summary = "添加目标", description = "为当前用户添加新的目标")
    public ResponseEntity<ApiResponse<String>> addGoal(
            @AuthenticationPrincipal User currentUser,
            @RequestParam @Parameter(description = "目标描述") String goal) {
        
        boolean success = userProfileService.addGoal(currentUser.getId(), goal);
        
        if (success) {
            return ResponseEntity.ok(ApiResponse.success("目标添加成功"));
        } else {
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("添加目标失败，可能已存在或用户资料不存在"));
        }
    }

    /**
     * 移除目标
     */
    @DeleteMapping("/me/goals")
    @Operation(summary = "移除目标", description = "移除当前用户的指定目标")
    public ResponseEntity<ApiResponse<String>> removeGoal(
            @AuthenticationPrincipal User currentUser,
            @RequestParam @Parameter(description = "目标描述") String goal) {
        
        boolean success = userProfileService.removeGoal(currentUser.getId(), goal);
        
        if (success) {
            return ResponseEntity.ok(ApiResponse.success("目标移除成功"));
        } else {
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("移除目标失败，可能不存在或用户资料不存在"));
        }
    }

    /**
     * 删除用户资料
     */
    @DeleteMapping("/me")
    @Operation(summary = "删除用户资料", description = "删除当前用户的详细资料")
    public ResponseEntity<ApiResponse<String>> deleteProfile(
            @AuthenticationPrincipal User currentUser) {
        
        boolean success = userProfileService.deleteByUserId(currentUser.getId());
        
        if (success) {
            return ResponseEntity.ok(ApiResponse.success("用户资料删除成功"));
        } else {
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("删除用户资料失败，资料可能不存在"));
        }
    }

    /**
     * 根据职业搜索用户资料（管理员功能）
     */
    @GetMapping("/search/occupation")
    @Operation(summary = "根据职业搜索用户", description = "根据职业搜索用户资料（管理员功能）")
    public ResponseEntity<ApiResponse<List<UserProfile>>> searchByOccupation(
            @AuthenticationPrincipal User currentUser,
            @RequestParam @Parameter(description = "职业名称") String occupation) {
        
        // 检查管理员权限
        if (!currentUser.isAdmin()) {
            return ResponseEntity.status(403)
                    .body(ApiResponse.error("权限不足"));
        }
        
        List<UserProfile> profiles = userProfileService.findByOccupation(occupation);
        return ResponseEntity.ok(ApiResponse.success(profiles));
    }

    /**
     * 根据地区搜索用户资料（管理员功能）
     */
    @GetMapping("/search/location")
    @Operation(summary = "根据地区搜索用户", description = "根据地区搜索用户资料（管理员功能）")
    public ResponseEntity<ApiResponse<List<UserProfile>>> searchByLocation(
            @AuthenticationPrincipal User currentUser,
            @RequestParam @Parameter(description = "地区名称") String location) {
        
        // 检查管理员权限
        if (!currentUser.isAdmin()) {
            return ResponseEntity.status(403)
                    .body(ApiResponse.error("权限不足"));
        }
        
        List<UserProfile> profiles = userProfileService.findByLocation(location);
        return ResponseEntity.ok(ApiResponse.success(profiles));
    }

    /**
     * 根据公司搜索用户资料（管理员功能）
     */
    @GetMapping("/search/company")
    @Operation(summary = "根据公司搜索用户", description = "根据公司搜索用户资料（管理员功能）")
    public ResponseEntity<ApiResponse<List<UserProfile>>> searchByCompany(
            @AuthenticationPrincipal User currentUser,
            @RequestParam @Parameter(description = "公司名称") String company) {
        
        // 检查管理员权限
        if (!currentUser.isAdmin()) {
            return ResponseEntity.status(403)
                    .body(ApiResponse.error("权限不足"));
        }
        
        List<UserProfile> profiles = userProfileService.findByCompany(company);
        return ResponseEntity.ok(ApiResponse.success(profiles));
    }
}
