package com.voicehub.backend.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * 通知实体类
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("notifications")
public class Notification extends BaseEntity {

    /**
     * 关联用户ID
     */
    @TableField("user_id")
    private Long userId;

    /**
     * 通知类型：REMINDER/SYSTEM/ACHIEVEMENT/MOOD_CHECK
     */
    @TableField("type")
    private String type;

    /**
     * 通知标题
     */
    @TableField("title")
    private String title;

    /**
     * 通知内容
     */
    @TableField("message")
    private String message;

    /**
     * 通知数据（JSON格式）
     */
    @TableField(value = "data", typeHandler = JacksonTypeHandler.class)
    private Map<String, Object> data;

    /**
     * 是否已读
     */
    @TableField("is_read")
    private Boolean isRead = false;

    /**
     * 优先级：LOW/NORMAL/HIGH/URGENT
     */
    @TableField("priority")
    private String priority = "NORMAL";

    /**
     * 计划发送时间
     */
    @TableField("scheduled_for")
    private LocalDateTime scheduledFor;

    /**
     * 过期时间
     */
    @TableField("expires_at")
    private LocalDateTime expiresAt;

    public Notification() {
        super();
    }

    public Notification(Long userId, String type, String title, String message) {
        this();
        this.userId = userId;
        this.type = type;
        this.title = title;
        this.message = message;
    }
}
