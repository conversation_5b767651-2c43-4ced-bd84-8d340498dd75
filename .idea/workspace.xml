<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="18a500c3-1ced-420a-8370-43ffa007a046" name="Changes" comment="chore:数据库脚本新增逻辑删除字段">
      <change afterPath="$PROJECT_DIR$/VoiceHub/backend/voicehub-backend/src/main/java/com/voicehub/backend/config/GlobalExceptionHandler.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/VoiceHub/backend/voicehub-backend/src/main/java/com/voicehub/backend/config/StartupConfig.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/VoiceHub/backend/voicehub-backend/src/main/java/com/voicehub/backend/controller/AudioFileController.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/VoiceHub/backend/voicehub-backend/src/main/java/com/voicehub/backend/controller/NotificationController.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/VoiceHub/backend/voicehub-backend/src/main/java/com/voicehub/backend/controller/UserProfileController.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/VoiceHub/backend/voicehub-backend/src/main/java/com/voicehub/backend/dto/ApiResponse.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/VoiceHub/backend/voicehub-backend/src/main/java/com/voicehub/backend/entity/AiTask.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/VoiceHub/backend/voicehub-backend/src/main/java/com/voicehub/backend/entity/ApiUsageLog.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/VoiceHub/backend/voicehub-backend/src/main/java/com/voicehub/backend/entity/AudioFile.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/VoiceHub/backend/voicehub-backend/src/main/java/com/voicehub/backend/entity/Notification.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/VoiceHub/backend/voicehub-backend/src/main/java/com/voicehub/backend/entity/SearchHistory.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/VoiceHub/backend/voicehub-backend/src/main/java/com/voicehub/backend/entity/SystemSetting.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/VoiceHub/backend/voicehub-backend/src/main/java/com/voicehub/backend/entity/UserAchievement.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/VoiceHub/backend/voicehub-backend/src/main/java/com/voicehub/backend/entity/UserProfile.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/VoiceHub/backend/voicehub-backend/src/main/java/com/voicehub/backend/entity/UserSession.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/VoiceHub/backend/voicehub-backend/src/main/java/com/voicehub/backend/mapper/AiTaskMapper.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/VoiceHub/backend/voicehub-backend/src/main/java/com/voicehub/backend/mapper/ApiUsageLogMapper.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/VoiceHub/backend/voicehub-backend/src/main/java/com/voicehub/backend/mapper/AudioFileMapper.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/VoiceHub/backend/voicehub-backend/src/main/java/com/voicehub/backend/mapper/NotificationMapper.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/VoiceHub/backend/voicehub-backend/src/main/java/com/voicehub/backend/mapper/SearchHistoryMapper.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/VoiceHub/backend/voicehub-backend/src/main/java/com/voicehub/backend/mapper/SystemSettingMapper.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/VoiceHub/backend/voicehub-backend/src/main/java/com/voicehub/backend/mapper/UserAchievementMapper.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/VoiceHub/backend/voicehub-backend/src/main/java/com/voicehub/backend/mapper/UserProfileMapper.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/VoiceHub/backend/voicehub-backend/src/main/java/com/voicehub/backend/mapper/UserSessionMapper.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/VoiceHub/backend/voicehub-backend/src/main/java/com/voicehub/backend/service/AiTaskService.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/VoiceHub/backend/voicehub-backend/src/main/java/com/voicehub/backend/service/AudioFileService.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/VoiceHub/backend/voicehub-backend/src/main/java/com/voicehub/backend/service/NotificationService.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/VoiceHub/backend/voicehub-backend/src/main/java/com/voicehub/backend/service/SystemSettingService.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/VoiceHub/backend/voicehub-backend/src/main/java/com/voicehub/backend/service/UserAchievementService.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/VoiceHub/backend/voicehub-backend/src/main/java/com/voicehub/backend/service/UserProfileService.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/VoiceHub/backend/voicehub-backend/pom.xml" beforeDir="false" afterPath="$PROJECT_DIR$/VoiceHub/backend/voicehub-backend/pom.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/VoiceHub/backend/voicehub-backend/src/main/java/com/voicehub/backend/config/SecurityConfig.java" beforeDir="false" afterPath="$PROJECT_DIR$/VoiceHub/backend/voicehub-backend/src/main/java/com/voicehub/backend/config/SecurityConfig.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/VoiceHub/backend/voicehub-backend/src/main/java/com/voicehub/backend/controller/AuthController.java" beforeDir="false" afterPath="$PROJECT_DIR$/VoiceHub/backend/voicehub-backend/src/main/java/com/voicehub/backend/controller/AuthController.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/VoiceHub/backend/voicehub-backend/src/main/java/com/voicehub/backend/controller/ScheduleController.java" beforeDir="false" afterPath="$PROJECT_DIR$/VoiceHub/backend/voicehub-backend/src/main/java/com/voicehub/backend/controller/ScheduleController.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/VoiceHub/backend/voicehub-backend/src/main/java/com/voicehub/backend/dto/AuthResponse.java" beforeDir="false" afterPath="$PROJECT_DIR$/VoiceHub/backend/voicehub-backend/src/main/java/com/voicehub/backend/dto/AuthResponse.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/VoiceHub/backend/voicehub-backend/src/main/java/com/voicehub/backend/dto/LoginRequest.java" beforeDir="false" afterPath="$PROJECT_DIR$/VoiceHub/backend/voicehub-backend/src/main/java/com/voicehub/backend/dto/LoginRequest.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/VoiceHub/backend/voicehub-backend/src/main/java/com/voicehub/backend/dto/RegisterRequest.java" beforeDir="false" afterPath="$PROJECT_DIR$/VoiceHub/backend/voicehub-backend/src/main/java/com/voicehub/backend/dto/RegisterRequest.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/VoiceHub/backend/voicehub-backend/src/main/java/com/voicehub/backend/entity/Schedule.java" beforeDir="false" afterPath="$PROJECT_DIR$/VoiceHub/backend/voicehub-backend/src/main/java/com/voicehub/backend/entity/Schedule.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/VoiceHub/backend/voicehub-backend/src/main/java/com/voicehub/backend/entity/User.java" beforeDir="false" afterPath="$PROJECT_DIR$/VoiceHub/backend/voicehub-backend/src/main/java/com/voicehub/backend/entity/User.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/VoiceHub/backend/voicehub-backend/src/main/java/com/voicehub/backend/entity/VoiceNote.java" beforeDir="false" afterPath="$PROJECT_DIR$/VoiceHub/backend/voicehub-backend/src/main/java/com/voicehub/backend/entity/VoiceNote.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/VoiceHub/backend/voicehub-backend/src/main/java/com/voicehub/backend/mapper/UserMapper.java" beforeDir="false" afterPath="$PROJECT_DIR$/VoiceHub/backend/voicehub-backend/src/main/java/com/voicehub/backend/mapper/UserMapper.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/VoiceHub/backend/voicehub-backend/src/main/java/com/voicehub/backend/security/JwtAuthenticationFilter.java" beforeDir="false" afterPath="$PROJECT_DIR$/VoiceHub/backend/voicehub-backend/src/main/java/com/voicehub/backend/security/JwtAuthenticationFilter.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/VoiceHub/backend/voicehub-backend/src/main/java/com/voicehub/backend/service/UserService.java" beforeDir="false" afterPath="$PROJECT_DIR$/VoiceHub/backend/voicehub-backend/src/main/java/com/voicehub/backend/service/UserService.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/VoiceHub/backend/voicehub-backend/src/main/resources/application.yml" beforeDir="false" afterPath="$PROJECT_DIR$/VoiceHub/backend/voicehub-backend/src/main/resources/application.yml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/VoiceHub/backend/voicehub-backend/src/test/java/com/voicehub/backend/service/MybatisPlusIntegrationTest.java" beforeDir="false" afterPath="$PROJECT_DIR$/VoiceHub/backend/voicehub-backend/src/test/java/com/voicehub/backend/service/MybatisPlusIntegrationTest.java" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="ChangesViewManager">
    <option name="groupingKeys">
      <option value="module" />
    </option>
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Class" />
      </list>
    </option>
  </component>
  <component name="Git.Settings">
    <option name="RECENT_BRANCH_BY_REPOSITORY">
      <map>
        <entry key="$PROJECT_DIR$/VoiceHub" value="main" />
      </map>
    </option>
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$/VoiceHub" />
  </component>
  <component name="GitHubPullRequestSearchHistory">{
  &quot;lastFilter&quot;: {
    &quot;state&quot;: &quot;OPEN&quot;,
    &quot;assignee&quot;: &quot;YKFire&quot;
  }
}</component>
  <component name="GithubPullRequestsUISettings">{
  &quot;selectedUrlAndAccountId&quot;: {
    &quot;url&quot;: &quot;https://github.com/YKFire/VoiceHub.git&quot;,
    &quot;accountId&quot;: &quot;a66d9689-02bb-447c-8b6d-f53d5c2137b2&quot;
  }
}</component>
  <component name="KubernetesApiPersistence">{}</component>
  <component name="KubernetesApiProvider">{
  &quot;isMigrated&quot;: true
}</component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="customMavenHome" value="$USER_HOME$/environment/apache-maven-3.9.9" />
        <option name="localRepository" value="$USER_HOME$/environment/maven-repository" />
        <option name="mavenHomeTypeForPersistence" value="CUSTOM" />
        <option name="userSettingsFile" value="$USER_HOME$/environment/apache-maven-3.9.9/conf/settings.xml" />
      </MavenGeneralSettings>
    </option>
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 6
}</component>
  <component name="ProjectId" id="30MSO8tMXnhX8mnGXkYf2OqBtbw" />
  <component name="ProjectLevelVcsManager" settingsEditedManually="true" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "Maven.voicehub-backend [clean,package].executor": "Run",
    "Maven.voicehub-backend [clean].executor": "Run",
    "Maven.voicehub-backend [package].executor": "Run",
    "RequestMappingsPanelOrder0": "0",
    "RequestMappingsPanelOrder1": "1",
    "RequestMappingsPanelWidth0": "75",
    "RequestMappingsPanelWidth1": "75",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "Spring Boot.VoiceHubBackendApplication.executor": "Run",
    "git-widget-placeholder": "dev/v1.0",
    "kotlin-language-version-configured": "true",
    "last_opened_file_path": "/Users/<USER>/IdeaProjects/buddyWork/VoiceHub",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "npm.dev.executor": "Run",
    "npm.start.executor": "Run",
    "npm.test.executor": "Run",
    "project.structure.last.edited": "SDKs",
    "project.structure.proportion": "0.15429688",
    "project.structure.side.proportion": "0.1922366",
    "settings.editor.selected.configurable": "MavenSettings",
    "ts.external.directory.path": "/Users/<USER>/IdeaProjects/buddyWork/VoiceHub/frontend/voicehub-ui/node_modules/typescript/lib",
    "vue.rearranger.settings.migration": "true"
  }
}]]></component>
  <component name="ReactorSettings">
    <option name="notificationShown" value="true" />
  </component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="$PROJECT_DIR$/VoiceHub" />
    </key>
  </component>
  <component name="RunManager" selected="Spring Boot.VoiceHubBackendApplication">
    <configuration name="VoiceHubBackendApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="voicehub-backend" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.voicehub.backend.VoiceHubBackendApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="dev" type="js.build_tools.npm" temporary="true" nameIsGenerated="true">
      <package-json value="$PROJECT_DIR$/VoiceHub/frontend/voicehub-ui/package.json" />
      <command value="run" />
      <scripts>
        <script value="dev" />
      </scripts>
      <node-interpreter value="project" />
      <envs />
      <method v="2" />
    </configuration>
    <configuration name="start" type="js.build_tools.npm" temporary="true" nameIsGenerated="true">
      <package-json value="$PROJECT_DIR$/VoiceHub/frontend/voicehub-frontend/package.json" />
      <command value="run" />
      <scripts>
        <script value="start" />
      </scripts>
      <node-interpreter value="project" />
      <envs />
      <method v="2" />
    </configuration>
    <configuration name="test" type="js.build_tools.npm" temporary="true" nameIsGenerated="true">
      <package-json value="$PROJECT_DIR$/VoiceHub/frontend/voicehub-frontend/package.json" />
      <command value="run" />
      <scripts>
        <script value="test" />
      </scripts>
      <node-interpreter value="project" />
      <envs />
      <method v="2" />
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="npm.dev" />
        <item itemvalue="npm.start" />
        <item itemvalue="npm.test" />
      </list>
    </recent_temporary>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9f38398b9061-39b83d9b5494-intellij.indexing.shared.core-IU-241.19072.14" />
        <option value="bundled-js-predefined-1d06a55b98c1-0b3e54e931b4-JavaScript-IU-241.19072.14" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="SvnConfiguration">
    <configuration>$USER_HOME$/.subversion</configuration>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="18a500c3-1ced-420a-8370-43ffa007a046" name="Changes" comment="" />
      <created>1728195414220</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1728195414220</updated>
      <workItem from="1738979266738" duration="14000" />
      <workItem from="1753435914438" duration="704000" />
      <workItem from="1753437282055" duration="914000" />
      <workItem from="1753512927684" duration="4720000" />
      <workItem from="1754152716829" duration="396000" />
      <workItem from="1754206231893" duration="11496000" />
      <workItem from="1754274963953" duration="52000" />
      <workItem from="1754286591472" duration="147000" />
      <workItem from="1754287455018" duration="101000" />
      <workItem from="1754465059092" duration="11000" />
      <workItem from="1754468648178" duration="5161000" />
      <workItem from="1754529217322" duration="7308000" />
      <workItem from="1754547627760" duration="12633000" />
      <workItem from="1754621172523" duration="362000" />
      <workItem from="1754633645023" duration="18000" />
      <workItem from="1754654448050" duration="10405000" />
      <workItem from="1754726240250" duration="9850000" />
      <workItem from="1754832032495" duration="2503000" />
      <workItem from="1754883203328" duration="173000" />
      <workItem from="1754912782482" duration="3696000" />
      <workItem from="1755175045703" duration="222000" />
      <workItem from="1755248314898" duration="6768000" />
      <workItem from="1755479120820" duration="98000" />
      <workItem from="1755500947835" duration="20000" />
      <workItem from="1755603775676" duration="2999000" />
      <workItem from="1755652468787" duration="9552000" />
      <workItem from="1755672963774" duration="4043000" />
      <workItem from="1755678273972" duration="420000" />
      <workItem from="1755679045561" duration="4036000" />
    </task>
    <task id="LOCAL-00001" summary="feat(frontend): 实现用户登录和注册功能&#10;&#10;- 新增 API 服务模块，用于处理登录和注册请求&#10;- 实现用户认证服务，包括登录、注册、登出等功能&#10;- 添加登录和注册表单组件，支持用户输入和表单验证&#10;- 集成 React Router，支持页面跳转&#10;- 使用 Lucide 图标库增强界面视觉效果">
      <option name="closed" value="true" />
      <created>1753436577002</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1753436577002</updated>
    </task>
    <task id="LOCAL-00002" summary="init: VoiceHub项目初始化">
      <option name="closed" value="true" />
      <created>1754227438881</created>
      <option name="number" value="00002" />
      <option name="presentableId" value="LOCAL-00002" />
      <option name="project" value="LOCAL" />
      <updated>1754227438881</updated>
    </task>
    <task id="LOCAL-00003" summary="fix:前后端项目启动问题修复,后台数据库框架从Hibernate迁移到MyBatis-Plus">
      <option name="closed" value="true" />
      <created>1754560122171</created>
      <option name="number" value="00003" />
      <option name="presentableId" value="LOCAL-00003" />
      <option name="project" value="LOCAL" />
      <updated>1754560122171</updated>
    </task>
    <task id="LOCAL-00004" summary="fix:修复用户注册登录功能">
      <option name="closed" value="true" />
      <created>1754725411415</created>
      <option name="number" value="00004" />
      <option name="presentableId" value="LOCAL-00004" />
      <option name="project" value="LOCAL" />
      <updated>1754725411415</updated>
    </task>
    <task id="LOCAL-00005" summary="fix:voicehub-ui项目启动异常修复">
      <option name="closed" value="true" />
      <created>1754821166589</created>
      <option name="number" value="00005" />
      <option name="presentableId" value="LOCAL-00005" />
      <option name="project" value="LOCAL" />
      <updated>1754821166589</updated>
    </task>
    <task id="LOCAL-00006" summary="fix:jwt登录异常修复">
      <option name="closed" value="true" />
      <created>1754833477674</created>
      <option name="number" value="00006" />
      <option name="presentableId" value="LOCAL-00006" />
      <option name="project" value="LOCAL" />
      <updated>1754833477674</updated>
    </task>
    <task id="LOCAL-00007" summary="refactor(voicehub-ui):界面UI/功能重构&#10;&#10;-- 项目结构优化&#10;-- 支持mock数据启动">
      <option name="closed" value="true" />
      <created>1755175191462</created>
      <option name="number" value="00007" />
      <option name="presentableId" value="LOCAL-00007" />
      <option name="project" value="LOCAL" />
      <updated>1755175191462</updated>
    </task>
    <task id="LOCAL-00008" summary="feat:voicehub-ui功能拓展">
      <option name="closed" value="true" />
      <created>1755479196401</created>
      <option name="number" value="00008" />
      <option name="presentableId" value="LOCAL-00008" />
      <option name="project" value="LOCAL" />
      <updated>1755479196401</updated>
    </task>
    <task id="LOCAL-00009" summary="chore:数据库脚本更新汇总，删除冗余文件">
      <option name="closed" value="true" />
      <created>1755654305356</created>
      <option name="number" value="00009" />
      <option name="presentableId" value="LOCAL-00009" />
      <option name="project" value="LOCAL" />
      <updated>1755654305356</updated>
    </task>
    <task id="LOCAL-00010" summary="chore:数据库脚本新增逻辑删除字段">
      <option name="closed" value="true" />
      <created>1755681229678</created>
      <option name="number" value="00010" />
      <option name="presentableId" value="LOCAL-00010" />
      <option name="project" value="LOCAL" />
      <updated>1755681229678</updated>
    </task>
    <option name="localTasksCounter" value="11" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State />
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="VcsManagerConfiguration">
    <ignored-roots>
      <path value="$PROJECT_DIR$/VoiceHub/frontend/voicehub-frontend" />
      <path value="$PROJECT_DIR$/VoiceHub" />
    </ignored-roots>
    <MESSAGE value="feat(frontend): 实现用户登录和注册功能&#10;&#10;- 新增 API 服务模块，用于处理登录和注册请求&#10;- 实现用户认证服务，包括登录、注册、登出等功能&#10;- 添加登录和注册表单组件，支持用户输入和表单验证&#10;- 集成 React Router，支持页面跳转&#10;- 使用 Lucide 图标库增强界面视觉效果" />
    <MESSAGE value="init: VoiceHub项目初始化" />
    <MESSAGE value="fix:前后端项目启动问题修复,后台数据库框架从Hibernate迁移到MyBatis-Plus" />
    <MESSAGE value="fix:修复用户注册登录功能" />
    <MESSAGE value="fix:voicehub-ui项目启动异常修复" />
    <MESSAGE value="fix:jwt登录异常修复" />
    <MESSAGE value="refactor(voicehub-ui):界面UI/功能重构&#10;&#10;-- 项目结构优化&#10;-- 支持mock数据启动" />
    <MESSAGE value="feat:voicehub-ui功能拓展" />
    <MESSAGE value="chore:数据库脚本更新汇总，删除冗余文件" />
    <MESSAGE value="chore:数据库脚本新增逻辑删除字段" />
    <option name="LAST_COMMIT_MESSAGE" value="chore:数据库脚本新增逻辑删除字段" />
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <breakpoint enabled="true" type="java-exception">
          <properties class="org.springframework.security.access.AccessDeniedException" package="org.springframework.security.access" />
          <option name="timeStamp" value="3" />
        </breakpoint>
      </breakpoints>
    </breakpoint-manager>
  </component>
  <component name="XSLT-Support.FileAssociations.UIState">
    <expand />
    <select />
  </component>
</project>